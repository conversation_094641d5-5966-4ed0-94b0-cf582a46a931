// backend/src/routes/bank-accounts/bank-accounts.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  UseGuards,
  Req,
  Query,
  ParseUUIDPipe,
  HttpCode,
  HttpStatus,
  ParseBoolPipe, // For parsing boolean query param
} from '@nestjs/common';
import { BankAccountsService } from './bank-accounts.service';
import { CreateBankAccountDto } from './dto/create-bank-account.dto';
import { UpdateBankAccountDto } from './dto/update-bank-account.dto';
import { JwtAuthGuard } from '../../middlewares/jwt-auth.guard';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery, ApiBody, ApiBearerAuth } from '@nestjs/swagger';
// Import RBAC guards if needed

// Define a type for the request object with the authenticated user
interface RequestWithUser extends Request {
  user: {
    id: string;
    companyId: string;
  };
}

@ApiTags('bank-accounts')
@ApiBearerAuth('JWT-auth')
@Controller('bank-accounts')
@UseGuards(JwtAuthGuard) // Apply JWT authentication
// Apply RBAC guards if needed
export class BankAccountsController {
  constructor(private readonly bankAccountsService: BankAccountsService) {}

  @Post()
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({ summary: 'Criar uma nova conta bancária' })
  @ApiResponse({ status: 201, description: 'Conta bancária criada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiBody({
    type: CreateBankAccountDto,
    description: 'Dados da conta bancária',
    examples: {
      example1: {
        summary: 'Exemplo de conta corrente',
        value: {
          bankId: '123e4567-e89b-12d3-a456-************', // ID de um banco existente
          accountNumber: '12345-6',
          accountType: 'corrente',
          balance: 1000.00,
          balanceDate: new Date().toISOString(),
          creditLimit: 500.00,
          currencyId: '123e4567-e89b-12d3-a456-************', // ID de uma moeda existente
          name: 'Conta Corrente Principal',
          isEnabled: true
        }
      },
      example2: {
        summary: 'Exemplo de conta poupança',
        value: {
          bankId: '123e4567-e89b-12d3-a456-************', // ID de um banco existente
          accountNumber: '98765-4',
          accountType: 'poupanca',
          balance: 5000.00,
          balanceDate: new Date().toISOString(),
          currencyId: '123e4567-e89b-12d3-a456-************', // ID de uma moeda existente
          name: 'Poupança',
          isEnabled: true
        }
      }
    }
  })
  create(
    @Body() createBankAccountDto: CreateBankAccountDto,
    @Req() req: RequestWithUser,
  ) {
    return this.bankAccountsService.create(createBankAccountDto, req.user);
  }

  @Get()
  @ApiOperation({ summary: 'Listar todas as contas bancárias' })
  @ApiResponse({ status: 200, description: 'Lista de contas bancárias retornada com sucesso' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiQuery({ name: 'page', required: false, description: 'Número da página', type: Number, example: 1 })
  @ApiQuery({ name: 'limit', required: false, description: 'Limite de itens por página', type: Number, example: 10 })
  @ApiQuery({ name: 'isEnabled', required: false, description: 'Filtrar por contas ativas/inativas', type: Boolean, example: true })
  @ApiQuery({ name: 'companyId', required: false, description: 'ID da empresa para filtrar contas', type: String, example: '123e4567-e89b-12d3-a456-************' })
  findAll(
    @Req() req: RequestWithUser,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
    @Query('isEnabled') isEnabled?: string, // Receive as string
    @Query('companyId') companyId?: string, // Adicionar parâmetro companyId
  ) {
     const pageNumber = page ? parseInt(page, 10) : 1;
     const limitNumber = limit ? parseInt(limit, 10) : 10;
     let isEnabledBool: boolean | undefined;

     if (isEnabled !== undefined) {
         if (isEnabled.toLowerCase() === 'true') {
             isEnabledBool = true;
         } else if (isEnabled.toLowerCase() === 'false') {
             isEnabledBool = false;
         }
         // If invalid boolean string, isEnabledBool remains undefined, effectively ignoring the filter
     }

     // Basic validation
     if (isNaN(pageNumber) || pageNumber < 1) {
         throw new Error('Invalid page number');
     }
      if (isNaN(limitNumber) || limitNumber < 1) {
         throw new Error('Invalid limit number');
     }

     // Usar o companyId da URL se fornecido, caso contrário usar o do usuário autenticado
     const effectiveCompanyId = companyId || req.user.companyId;
     console.log(`[BankAccountsController] Using companyId: ${effectiveCompanyId} (from URL: ${companyId}, from user: ${req.user.companyId})`);

    return this.bankAccountsService.findAll(
      { ...req.user, companyId: effectiveCompanyId }, // Sobrescrever o companyId do usuário
      {
        page: pageNumber,
        limit: limitNumber,
        isEnabled: isEnabledBool
      });
  }

  @Get(':id')
  @ApiOperation({ summary: 'Obter detalhes de uma conta bancária específica' })
  @ApiResponse({ status: 200, description: 'Detalhes da conta bancária retornados com sucesso' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 404, description: 'Conta bancária não encontrada' })
  @ApiParam({ name: 'id', description: 'ID da conta bancária', type: 'string', format: 'uuid', example: '123e4567-e89b-12d3-a456-************' })
  findOne(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.bankAccountsService.findOne(id, req.user);
  }

  @Put(':id')
  @ApiOperation({ summary: 'Atualizar uma conta bancária' })
  @ApiResponse({ status: 200, description: 'Conta bancária atualizada com sucesso' })
  @ApiResponse({ status: 400, description: 'Dados inválidos' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 404, description: 'Conta bancária não encontrada' })
  @ApiParam({ name: 'id', description: 'ID da conta bancária', type: 'string', format: 'uuid', example: '123e4567-e89b-12d3-a456-************' })
  @ApiBody({
    type: UpdateBankAccountDto,
    description: 'Dados para atualização da conta bancária',
    examples: {
      example1: {
        summary: 'Atualizar nome e limite de crédito',
        value: {
          name: 'Conta Corrente Atualizada',
          creditLimit: 1000.00,
          isEnabled: true
        }
      },
      example2: {
        summary: 'Atualizar tipo de conta',
        value: {
          accountType: 'investimento',
          name: 'Conta de Investimentos'
        }
      },
      example3: {
        summary: 'Atualizar banco e número da conta',
        value: {
          bankId: '123e4567-e89b-12d3-a456-************', // ID de um banco existente
          accountNumber: '54321-0'
        }
      }
    }
  })
  update(
    @Param('id', ParseUUIDPipe) id: string,
    @Body() updateBankAccountDto: UpdateBankAccountDto,
    @Req() req: RequestWithUser,
  ) {
    console.log(`[BankAccountsController] UPDATE request received for ID: ${id}`);
    console.log(`[BankAccountsController] Update DTO:`, JSON.stringify(updateBankAccountDto, null, 2));
    console.log(`[BankAccountsController] User:`, JSON.stringify({
      id: req.user.id,
      companyId: req.user.companyId
    }, null, 2));

    return this.bankAccountsService.update(id, updateBankAccountDto, req.user);
  }

  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Remover uma conta bancária (soft delete)' })
  @ApiResponse({ status: 204, description: 'Conta bancária removida com sucesso' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 404, description: 'Conta bancária não encontrada' })
  @ApiParam({ name: 'id', description: 'ID da conta bancária', type: 'string', format: 'uuid', example: '123e4567-e89b-12d3-a456-************' })
  remove(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.bankAccountsService.remove(id, req.user);
  }

  @Get(':id/balance')
  @ApiOperation({ summary: 'Obter o saldo atual de uma conta bancária' })
  @ApiResponse({ status: 200, description: 'Saldo da conta bancária retornado com sucesso' })
  @ApiResponse({ status: 401, description: 'Não autorizado' })
  @ApiResponse({ status: 404, description: 'Conta bancária não encontrada' })
  @ApiParam({ name: 'id', description: 'ID da conta bancária', type: 'string', format: 'uuid', example: '123e4567-e89b-12d3-a456-************' })
  getBalance(
    @Param('id', ParseUUIDPipe) id: string,
    @Req() req: RequestWithUser,
  ) {
    return this.bankAccountsService.getBalance(id, req.user);
  }
}