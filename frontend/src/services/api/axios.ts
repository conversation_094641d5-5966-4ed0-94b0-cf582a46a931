import axios, { AxiosError, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import tokenStorage from '@/services/api/tokenStorage';

// Import o inicializador do monitoringService para garantir que esteja disponível globalmente
import '@/services/initializers/monitoringInitializer';

// Interface personalizada para estender a configuração do Axios
interface CustomInternalAxiosRequestConfig extends InternalAxiosRequestConfig {
  metadata?: {
    startTime: number;
    requestId: string;
  };
  _retry?: boolean;
}

// Configuração base do Axios
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:3000/api', // Usar localhost para acesso do navegador
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true, // Necessário para CORS com credenciais
  timeout: 10000, // 10 segundos de timeout
});

// Log para depuração
// console.log(`API configurada para URL: ${import.meta.env.VITE_API_URL || 'http://localhost:3000/api'}`);

// Log para depuração desativado
// if (import.meta.env.DEV) {
//   console.log(`API configurada para URL: ${import.meta.env.VITE_API_URL || 'http://localhost:3000/api'}`);
// }

// Exportar a instância api como default para compatibilidade com importações existentes
// Exportar como default para compatibilidade com importações existentes
export default api;
// Também exportar como constante nomeada para permitir importações nomeadas
export { api };

// Interceptor para adicionar token e monitorar requisições
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // Registrar início da requisição
    const requestId = Date.now().toString(36) + Math.random().toString(36).substring(2);
    config.headers['X-Request-ID'] = requestId;

    // Adicionar timestamp para cálculo de duração
    (config as CustomInternalAxiosRequestConfig).metadata = { startTime: Date.now(), requestId };

    // Adicionar token de autenticação se disponível
    const token = tokenStorage.getAccessToken();
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }

    // Adicionar o companyId como header X-Company-ID
    const activeCompanyId = localStorage.getItem('activeCompanyId');
    
    if (process.env.NODE_ENV === 'development') {
      console.log('[Axios Interceptor] activeCompanyId from localStorage:', activeCompanyId);
      console.log('[Axios Interceptor] Request URL:', config.url);
    }
    
    if (activeCompanyId && activeCompanyId !== 'null' && activeCompanyId !== 'undefined') {
      config.headers['X-Company-ID'] = activeCompanyId;
      if (process.env.NODE_ENV === 'development') {
        console.log('[Axios Interceptor] Added X-Company-ID header:', activeCompanyId);
      }
    } else if (process.env.NODE_ENV === 'development') {
      console.warn('[Axios Interceptor] No valid activeCompanyId found in localStorage');
    }

    // Registrar evento de requisição usando o serviço de monitoramento via objeto global
    if (typeof window !== 'undefined' && window.monitoringService) {
      window.monitoringService.recordApiRequest({
      url: config.url,
      method: config.method?.toUpperCase(),
      requestId: requestId
      });
    }

    return config;
  },
  (error) => {
    // Registrar erro de requisição usando o serviço de monitoramento via objeto global
    if (typeof window !== 'undefined' && window.monitoringService) {
      window.monitoringService.recordApiResponseError({
      error: error.message,
      requestId: 'unknown'
      });
    }

    console.error('Erro na preparação da requisição:', error.message);

    return Promise.reject(error);
  }
);

// Interceptor para monitorar respostas e tratar erros
api.interceptors.response.use(
  (response: AxiosResponse) => {
    // Calcular duração da requisição
    const config = response.config as CustomInternalAxiosRequestConfig;
    const duration = config.metadata ? Date.now() - config.metadata.startTime : 0;
    const requestId = config.metadata?.requestId || 'unknown';

    // Registrar sucesso de requisição usando o serviço de monitoramento via objeto global
    if (typeof window !== 'undefined' && window.monitoringService) {
      window.monitoringService.recordApiResponseSuccess({
      url: response.config.url,
      method: response.config.method?.toUpperCase(),
      status: response.status,
      duration,
      requestId,
      dataSize: JSON.stringify(response.data).length
      });
    }

    // Log de requisição bem-sucedida desativado

    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as CustomInternalAxiosRequestConfig;
    const duration = originalRequest.metadata ? Date.now() - originalRequest.metadata.startTime : 0;
    const requestId = originalRequest.metadata?.requestId || 'unknown';

    // Registrar erro de requisição usando o serviço de monitoramento via objeto global
    if (typeof window !== 'undefined' && window.monitoringService) {
      window.monitoringService.recordApiResponseError({
      url: originalRequest.url,
      method: originalRequest.method?.toUpperCase(),
      status: error.response?.status,
      duration,
      requestId,
      error: error.message
      });
    }

    // Log de erro na requisição desativado

    // Se for erro 401 (não autorizado) e não for uma requisição de refresh token
    if (error.response?.status === 401 &&
        !originalRequest._retry &&
        originalRequest.url !== '/auth/refresh-token') {

      originalRequest._retry = true;

      try {
        // Registrar tentativa de refresh de token
        if (typeof window !== 'undefined' && window.monitoringService) {
          window.monitoringService.recordAuthEvent('AUTH_REFRESH_TOKEN', {
          action: 'refresh_token_attempt'
          });
        }

        const refreshToken = tokenStorage.getRefreshToken();

        if (!refreshToken) {
          // Se não tiver refresh token, deslogar usuário
          tokenStorage.clearTokens();
          // Registrar logout forçado por falta de refresh token
          if (typeof window !== 'undefined' && window.monitoringService) {
            window.monitoringService.recordAuthEvent('AUTH_LOGOUT', {
            action: 'forced_logout',
            success: true,
            error: 'No refresh token available'
            });
          }

          // Redirecionar para login apenas se estiver no browser
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }

          return Promise.reject(error);
        }

        // Tenta obter novo token
        const response = await axios.post(
          `${import.meta.env.VITE_API_URL || 'http://localhost:3000/api'}/auth/refresh-token`,
          { refreshToken }
        );

        if (response.data.accessToken) {
          // Armazenar novos tokens com tempo de expiração (1 hora por padrão)
          tokenStorage.setTokens({
            accessToken: response.data.accessToken,
            refreshToken: response.data.refreshToken || refreshToken
          }, 3600);

          console.log('Token renovado automaticamente pelo interceptor');

          // Registrar sucesso no refresh de token
          if (typeof window !== 'undefined' && window.monitoringService) {
            window.monitoringService.recordAuthEvent('AUTH_REFRESH_TOKEN', {
            action: 'refresh_token_success',
            success: true
            });
          }

          // Atualiza header e refaz requisição original
          if (originalRequest.headers) {
            originalRequest.headers['Authorization'] = `Bearer ${response.data.accessToken}`;
          }

          return api(originalRequest);
        } else {
          // Se não vier token na resposta, deslogar usuário
          tokenStorage.clearTokens();
          // Registrar logout forçado por resposta inválida
          if (typeof window !== 'undefined' && window.monitoringService) {
            window.monitoringService.recordAuthEvent('AUTH_LOGOUT', {
            action: 'forced_logout',
            success: true,
            error: 'Invalid refresh token response'
            });
          }

          // Redirecionar para login apenas se estiver no browser
          if (typeof window !== 'undefined') {
            window.location.href = '/login';
          }

          return Promise.reject(error);
        }
      } catch (refreshError) {
        // Em caso de erro no refresh, deslogar usuário
        tokenStorage.clearTokens();
        // Registrar erro no refresh de token
        if (typeof window !== 'undefined' && window.monitoringService) {
          window.monitoringService.recordAuthEvent('AUTH_REFRESH_TOKEN_ERROR', {
            action: 'refresh_token_error',
            success: false,
            error: refreshError instanceof Error ? refreshError.message : 'Unknown error'
          });
        }

        // Redirecionar para login apenas se estiver no browser
        if (typeof window !== 'undefined') {
            window.location.href = '/login';
        }

        return Promise.reject(refreshError);
      }
    }

    // Para erros 403 (Forbidden), log apenas em desenvolvimento
    // Log de permissão negada desativado

    return Promise.reject(error);
  }
);

