import React, { createContext, useContext, useState, useEffect } from 'react';
import { User, LoginRequest, RegisterRequest, Company } from '@/types/api';
import { authService } from '@/services/api/authService';
import { userService } from '@/services/api/userService';
import { companyService } from '@/services/api/companyService';
import tokenStorage from '@/services/api/tokenStorage';
import { useNavigate } from 'react-router-dom';
import { toast } from 'sonner';
import { monitoringService, EventType } from '@/services/monitoring/monitoringService';

type AuthContextType = {
  user: User | null;
  loading: boolean;
  companies: Company[] | null;
  activeCompanyId: string | null;
  login: (data: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => Promise<void>;
  forgotPassword: (email: string) => Promise<void>;
  resetPassword: (token: string, password: string, passwordConfirmation: string) => Promise<void>;
  switchCompany: (companyId: string) => void;
  isAuthenticated: boolean;
  checkAuthStatus: () => Promise<boolean>;
  // Indica se a verificação de autenticação inicial foi concluída
  initialized: boolean;
  // Indica se o perfil do usuário está sendo carregado
  loadingProfile: boolean;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Função simples para tratamento de erros dentro do AuthProvider
const handleAuthError = (error: any) => {
  console.error('Auth Error:', error);
  const message = error?.response?.data?.message || 'Ocorreu um erro inesperado.';
  toast.error(message);
  return { error, message };
};

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [companies, setCompanies] = useState<Company[] | null>(null);
  const [activeCompanyId, setActiveCompanyId] = useState<string | null>(localStorage.getItem('activeCompanyId'));
  const [loading, setLoading] = useState(true);
  // Estado para controlar se a verificação de autenticação inicial foi concluída
  const [initialized, setInitialized] = useState(false);
  // Estado para controlar se o perfil do usuário está sendo carregado
  const [loadingProfile, setLoadingProfile] = useState(false);
  // Estado explícito para controlar a autenticação
  // Inicializar com o valor do localStorage, se existir
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(
    localStorage.getItem('isAuthenticated') === 'true'
  );
  const navigate = useNavigate();

  // Função para registrar eventos de autenticação para fins de monitoramento
  // Agora usando o serviço de monitoramento baseado em eventos
  const logAuthEvent = (event: string, details?: any) => {
    // console.log(`[Auth] ${event}`, details);

    // Mapear eventos do contexto para os tipos de eventos do serviço de monitoramento
    let eventType: EventType;
    switch (event) {
      case 'auth_check_success':
        eventType = EventType.AUTH_LOGIN;
        break;
      case 'auth_check_failed':
        eventType = EventType.AUTH_LOGIN_ERROR;
        break;
      case 'login_success':
        eventType = EventType.AUTH_LOGIN;
        break;
      case 'login_failed':
        eventType = EventType.AUTH_LOGIN_ERROR;
        break;
      case 'register_success':
        eventType = EventType.AUTH_REGISTER;
        break;
      case 'register_failed':
        eventType = EventType.AUTH_REGISTER_ERROR;
        break;
      case 'logout_success':
        eventType = EventType.AUTH_LOGOUT;
        break;
      case 'logout_error':
        eventType = EventType.AUTH_LOGOUT_ERROR;
        break;
      case 'forgot_password_request':
        eventType = EventType.AUTH_PASSWORD_RESET_REQUEST;
        break;
      case 'forgot_password_failed':
        eventType = EventType.AUTH_PASSWORD_RESET_ERROR;
        break;
      case 'password_reset_success':
        eventType = EventType.AUTH_PASSWORD_RESET;
        break;
      case 'password_reset_failed':
        eventType = EventType.AUTH_PASSWORD_RESET_ERROR;
        break;
      default:
        eventType = EventType.ERROR;
    }

    // Enviar evento para o serviço de monitoramento
    monitoringService.recordAuthEvent(eventType, {
      ...details,
      action: event,
      success: !event.includes('failed') && !event.includes('error')
    });
  };

  // Variável para controlar se já estamos verificando a autenticação
  // Isso evita múltiplas verificações simultâneas
  const [isCheckingAuth, setIsCheckingAuth] = useState(false);

  // Função para verificar o status de autenticação
  const checkAuthStatus = async (): Promise<boolean> => {
    // Se já estamos verificando ou se o usuário já está autenticado, retornar imediatamente
    if (isCheckingAuth) {
      // console.log('[AuthContext] Já está verificando autenticação, ignorando chamada duplicada');
      return !!user;
    }

    // Se o usuário já está autenticado, não precisamos verificar novamente
    if (user) {
      // console.log('[AuthContext] Usuário já está autenticado, pulando verificação');
      return true;
    }

    try {
      setIsCheckingAuth(true);
      setLoading(true);
      // console.log('[AuthContext] Verificando status de autenticação');

      // Verificar se temos tokens armazenados
      if (!authService.tokenStorage.hasTokens()) {
        // console.log('[AuthContext] Tokens não encontrados');
        setUser(null);
        setCompanies(null);
        setActiveCompanyId(null);
        return false;
      }

      // Verificar se o token está expirado
      if (authService.tokenStorage.isTokenExpired()) {
        // console.log('[AuthContext] Token expirado, tentando renovar');
        // Tentar renovar o token
        const refreshResult = await authService.refreshToken();

        if (!refreshResult) {
          // console.log('[AuthContext] Falha ao renovar token');
          setUser(null);
          setCompanies(null);
          setActiveCompanyId(null);
          return false;
        }

        // console.log('[AuthContext] Token renovado com sucesso');
        setUser(refreshResult.user);

        // Buscar empresas do usuário
        try {
          console.log('[AuthContext] Buscando empresas do usuário');
          const companiesData = await loadCompanies();
          setCompanies(companiesData);

          // Verificar se temos uma empresa ativa salva
          const savedCompanyId = localStorage.getItem('activeCompanyId');
          if (savedCompanyId && companiesData.some(company => company.id === savedCompanyId)) {
            console.log('[AuthContext] Usando empresa salva:', savedCompanyId);
            setActiveCompanyId(savedCompanyId);
          } else if (companiesData.length > 0) {
            // Se não tiver empresa ativa ou ela não existir mais, usar a primeira
            console.log('[AuthContext] Usando primeira empresa:', companiesData[0].id);
            setActiveCompanyId(companiesData[0].id);
            localStorage.setItem('activeCompanyId', companiesData[0].id);
          }
        } catch (error) {
          console.error('[AuthContext] Erro ao buscar empresas:', error);
        }

        return true;
      }

      // Token válido, buscar perfil do usuário
      // console.log('[AuthContext] Token válido, buscando perfil do usuário');
      try {
        // Definir o estado de carregamento do perfil
        setLoadingProfile(true);

        // Criar uma promessa com timeout para evitar que a chamada fique presa
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => {
            reject(new Error('Timeout ao buscar perfil do usuário'));
          }, 5000); // 5 segundos de timeout
        });

        // Buscar perfil do usuário com timeout
        const userData = await Promise.race([
          userService.getProfile(),
          timeoutPromise
        ]);

        // console.log('[AuthContext] Perfil do usuário obtido com sucesso:', userData);

        // Verificar se o usuário retornado é válido (tem ID)
        if (userData && (userData as User).id) {
          // Atualizar o estado user e forçar a atualização do estado isAuthenticated
          setUser(userData as User);
          setIsAuthenticated(true);
          return true;
        } else {
          console.warn('[AuthContext] Dados do usuário inválidos ou incompletos');
          setUser(null);
          setIsAuthenticated(false);
          return false;
        }
      } catch (error) {
        console.error('[AuthContext] Erro ao buscar perfil do usuário:', error);
        setUser(null);
        setIsAuthenticated(false);
        return false; // Retornar false em vez de lançar erro
      } finally {
        // Garantir que o estado de carregamento do perfil seja definido como false
        setLoadingProfile(false);
      }

      // Buscar empresas do usuário
      try {
        console.log('[AuthContext] Buscando empresas do usuário após verificar perfil');
        const companiesData = await loadCompanies();
        setCompanies(companiesData);

        // Verificar se temos uma empresa ativa salva
        const savedCompanyId = localStorage.getItem('activeCompanyId');
        if (savedCompanyId && companiesData.some(company => company.id === savedCompanyId)) {
          console.log('[AuthContext] Usando empresa salva:', savedCompanyId);
          setActiveCompanyId(savedCompanyId);
        } else if (companiesData.length > 0) {
          // Se não tiver empresa ativa ou ela não existir mais, usar a primeira
          console.log('[AuthContext] Usando primeira empresa:', companiesData[0].id);
          setActiveCompanyId(companiesData[0].id);
          localStorage.setItem('activeCompanyId', companiesData[0].id);
        }
      } catch (error) {
        console.error('[AuthContext] Erro ao buscar empresas:', error);
      }

      // console.log('[AuthContext] Verificação de autenticação concluída com sucesso');
      logAuthEvent('auth_check_success');

      // Marcar que a verificação de autenticação inicial foi concluída
      // console.log('[AuthContext] Definindo initialized como true após verificação bem-sucedida');
      setInitialized(true);

      return true;
    } catch (error) {
      logAuthEvent('auth_check_failed', { error });
      handleAuthError(error);
      setUser(null);
      setCompanies(null);
      setActiveCompanyId(null);
      return false;
    } finally {
      setLoading(false);
      setIsCheckingAuth(false);

      // Garantir que initialized seja definido como true mesmo em caso de erro
      // Isso evita que o usuário fique preso em um estado de carregamento
      // console.log('[AuthContext] Definindo initialized como true no finally');
      setInitialized(true);
    }
  };

  // Verificar se os tokens existem e são válidos no início da aplicação
  const checkTokensOnStartup = () => {
    // Verificar se temos um estado de autenticação persistido
    const persistedAuth = localStorage.getItem('isAuthenticated') === 'true';
    // console.log('[AuthContext] Estado de autenticação persistido:', persistedAuth);

    // Verificar se temos tokens armazenados
    if (!authService.tokenStorage.hasTokens()) {
      // console.log('[AuthContext] Tokens não encontrados no início da aplicação');
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('isAuthenticated');
      setInitialized(true);
      setLoading(false);
      return;
    }

    // Verificar se o token está expirado
    if (authService.tokenStorage.isTokenExpired()) {
      // console.log('[AuthContext] Token expirado no início da aplicação');
      setUser(null);
      setIsAuthenticated(false);
      localStorage.removeItem('isAuthenticated');
      setInitialized(true);
      setLoading(false);
      return;
    }

    // Se temos um estado de autenticação persistido, definir isAuthenticated como true
    // mesmo antes de verificar o perfil do usuário
    if (persistedAuth) {
      // console.log('[AuthContext] Definindo isAuthenticated como true com base no estado persistido');
      setIsAuthenticated(true);
    }

    // Se os tokens existem e são válidos, verificar o status de autenticação
    // console.log('[AuthContext] Tokens válidos encontrados no início da aplicação');
    // Usar setTimeout para evitar chamadas recursivas e garantir que o estado seja atualizado
    setTimeout(() => {
      checkAuthStatus();
    }, 0);
  };

  // Verificar autenticação ao carregar a página
  useEffect(() => {
    // console.log('[AuthContext] Verificando autenticação inicial');
    checkTokensOnStartup();
  }, []);

  // Monitorar o estado initialized
  useEffect(() => {
    // console.log('[AuthContext] Estado initialized mudou para:', initialized);
  }, [initialized]);

  // Monitorar o estado isAuthenticated
  useEffect(() => {
    // console.log('[AuthContext] Estado isAuthenticated mudou para:', isAuthenticated);
  }, [isAuthenticated]);

  const login = async (data: LoginRequest) => {
    setLoading(true);
    try {
      // console.log('[AuthContext] Iniciando processo de login para:', data.email);
      logAuthEvent('login_attempt', { email: data.email });

      // Fazer login e obter token e perfil do usuário
      const response = await authService.login(data);
      // console.log('[AuthContext] Login bem-sucedido, usuário autenticado');

      // Definir usuário no estado
      setUser(response.user);

      // Garantir que initialized seja definido como true
      setInitialized(true);

      // Buscar empresas do usuário
      // console.log('[AuthContext] Buscando empresas do usuário');
      const companiesData = await companyService.getCompanies();
      // console.log('[AuthContext] Empresas recebidas:', companiesData);

      // Verificar se a resposta tem a estrutura esperada
      if (!companiesData) {
        console.error('[AuthContext] Resposta de empresas inválida:', companiesData);
        setCompanies([]);
      } else {
        // A API retorna {items: [...], total, page, limit, totalPages}
        setCompanies(companiesData.data || []);
      }

      // Definir empresa ativa (primeira ou última usada)
      const savedCompanyId = localStorage.getItem('activeCompanyId');
      // console.log('[AuthContext] Empresa salva anteriormente:', savedCompanyId);

      // Verificar se temos empresas válidas
      if (companiesData && companiesData.data && companiesData.data.length > 0) {
        if (savedCompanyId && companiesData.data.some(company => company.id === savedCompanyId)) {
          // console.log('[AuthContext] Usando empresa salva anteriormente:', savedCompanyId);
          setActiveCompanyId(savedCompanyId);
        } else {
          // console.log('[AuthContext] Usando primeira empresa:', companiesData.data[0].id);
          setActiveCompanyId(companiesData.data[0].id);
          localStorage.setItem('activeCompanyId', companiesData.data[0].id);
        }
      } else {
        // Se não houver empresas, limpar o activeCompanyId
        // console.log('[AuthContext] Nenhuma empresa encontrada');
        setActiveCompanyId(null);
      }

      // console.log('[AuthContext] Login concluído com sucesso');
      toast.success('Login realizado com sucesso!');
      logAuthEvent('login_success', { userId: response.user.id });

      // Redirecionar para a página inicial ou página anterior
      const redirectPath = sessionStorage.getItem('redirectAfterLogin') || '/';
      sessionStorage.removeItem('redirectAfterLogin');
      // console.log('[AuthContext] Redirecionando para:', redirectPath);

      // Forçar a atualização do estado isAuthenticated antes de redirecionar
      setIsAuthenticated(true);

      // Armazenar o caminho de redirecionamento em uma variável local para evitar problemas de closure
      const finalRedirectPath = redirectPath;

      // Usar setTimeout com um atraso maior para garantir que o estado seja atualizado antes do redirecionamento
      setTimeout(() => {
        // console.log('[AuthContext] Executando redirecionamento para:', finalRedirectPath);
        // Verificar novamente se o usuário ainda está autenticado antes de redirecionar
        if (user) {
          navigate(finalRedirectPath, { replace: true });
        } else {
          // console.log('[AuthContext] Usuário não está mais autenticado, cancelando redirecionamento');
        }
      }, 300);
    } catch (error: any) {
      logAuthEvent('login_failed', { email: data.email, error: error.message });
      handleAuthError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const register = async (data: RegisterRequest) => {
    setLoading(true);
    try {
      await authService.register(data);
      toast.success('Conta criada com sucesso! Faça login para continuar.');
      logAuthEvent('register_success', { email: data.email });
      navigate('/login');
    } catch (error: any) {
      logAuthEvent('register_failed', { email: data.email, error: error.message });
      handleAuthError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    setLoading(true);
    try {
      // console.log('[AuthContext] Iniciando processo de logout');
      logAuthEvent('logout_attempt', user ? { userId: user.id } : undefined);

      await authService.logout();
      logAuthEvent('logout_success', user ? { userId: user.id } : undefined);

      // Limpar estados
      setUser(null);
      setCompanies(null);
      setActiveCompanyId(null);

      // Garantir que os tokens sejam removidos
      tokenStorage.clearTokens();

      // Remover o estado de autenticação do localStorage
      localStorage.removeItem('isAuthenticated');

      // console.log('[AuthContext] Logout realizado com sucesso');
      toast.success('Logout realizado com sucesso!');
      navigate('/login');
    } catch (error) {
      console.error('[AuthContext] Erro ao fazer logout:', error);
      logAuthEvent('logout_error', { error });

      // Mesmo com erro, limpar o estado de autenticação e tokens
      setUser(null);
      setCompanies(null);
      setActiveCompanyId(null);
      tokenStorage.clearTokens();

      navigate('/login');
    } finally {
      setLoading(false);
    }
  };

  // Função para solicitar recuperação de senha
  const forgotPassword = async (email: string) => {
    setLoading(true);
    try {
      await authService.forgotPassword(email);
      logAuthEvent('forgot_password_request', { email });
      toast.success('Instruções de recuperação enviadas para seu e-mail');
    } catch (error) {
      logAuthEvent('forgot_password_failed', { email, error });
      handleAuthError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Função para redefinir senha
  const resetPassword = async (token: string, password: string, passwordConfirmation: string) => {
    setLoading(true);
    try {
      await authService.resetPassword(token, password, passwordConfirmation);
      logAuthEvent('password_reset_success');
      toast.success('Senha redefinida com sucesso! Faça login para continuar.');
      navigate('/login');
    } catch (error) {
      logAuthEvent('password_reset_failed', { error });
      handleAuthError(error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Função para trocar de empresa
  const switchCompany = (companyId: string) => {
    if (companies && companies.some(company => company.id === companyId)) {
      console.log('[AuthContext] Switching to company:', companyId);
      setActiveCompanyId(companyId);
      localStorage.setItem('activeCompanyId', companyId);
      console.log('[AuthContext] activeCompanyId saved to localStorage:', companyId);
      toast.success('Empresa alterada com sucesso!');

      // Registrar evento de troca de empresa
      monitoringService.recordEvent('company_switch', {
        userId: user?.id,
        companyId
      });
    } else {
      toast.error('Empresa não encontrada');
    }
  };

  // Garantir que o activeCompanyId seja sempre sincronizado com o localStorage
  useEffect(() => {
    if (activeCompanyId) {
      console.log('[AuthContext] Syncing activeCompanyId to localStorage:', activeCompanyId);
      localStorage.setItem('activeCompanyId', activeCompanyId);
    } else {
      console.log('[AuthContext] Removing activeCompanyId from localStorage');
      localStorage.removeItem('activeCompanyId');
    }
  }, [activeCompanyId]);

  // Atualizar isAuthenticated sempre que o usuário mudar
  useEffect(() => {
    const authState = !!user;
    // console.log('[AuthContext] Estado de autenticação atualizado:', authState ? 'Autenticado' : 'Não autenticado', 'User:', user);

    // Usar um timeout para garantir que o estado seja atualizado após todas as outras operações
    // Isso evita problemas de sincronização entre diferentes componentes
    setTimeout(() => {
      // console.log('[AuthContext] Definindo isAuthenticated para:', authState);
      setIsAuthenticated(authState);

      // Persistir o estado de autenticação no localStorage
      if (authState) {
        localStorage.setItem('isAuthenticated', 'true');
      } else {
        localStorage.removeItem('isAuthenticated');
      }
    }, 0);
  }, [user]);

  // Função para carregar empresas do usuário
  const loadCompanies = async (): Promise<Company[]> => {
    try {
      console.log('[AuthContext] Carregando empresas...');
      const companiesData = await companyService.getCompanies(1, 100); // Buscar todas as empresas (limite alto)
      console.log('[AuthContext] Empresas carregadas:', companiesData);
      console.log('[AuthContext] Total de empresas:', companiesData.total);
      console.log('[AuthContext] Dados das empresas:', companiesData.data);
      
      if (companiesData && companiesData.data) {
        console.log('[AuthContext] Retornando empresas:', companiesData.data);
        return companiesData.data;
      }
      
      console.warn('[AuthContext] Nenhuma empresa encontrada ou estrutura inválida');
      return [];
    } catch (error) {
      console.error('[AuthContext] Erro ao carregar empresas:', error);
      
      // Registrar evento de erro
      monitoringService.recordEvent('auth_load_companies_error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId: user?.id
      });
      
      throw error;
    }
  };

  const value = {
    user,
    companies,
    activeCompanyId,
    loading,
    login,
    register,
    logout,
    forgotPassword,
    resetPassword,
    switchCompany,
    isAuthenticated,
    checkAuthStatus,
    initialized,
    loadingProfile,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth deve ser usado dentro de um AuthProvider');
  }
  return context;
};
