
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { toast as useToast } from "@/hooks/use-toast";
import { toast } from "react-toastify";
import { Transaction as ApiTransaction, CreateTransactionRequest } from "@/types/api";
import { TransactionFormValues } from "@/components/transactions/TransactionForm";
import { transactionService } from "@/services/api/transactionService";
import { useActiveCompany } from "@/hooks/useActiveCompany";

interface UseTransactionDetailProps {
  id?: string;
  mode?: string;
}

export function useTransactionDetail({ id, mode }: UseTransactionDetailProps) {
  const navigate = useNavigate();
  const activeCompanyId = useActiveCompany();
  const [isLoading, setIsLoading] = useState(true);
  const [transaction, setTransaction] = useState<ApiTransaction | null>(null);
  
  const isCreating = mode === "new";
  const isEditing = mode === "edit";
  const isViewing = mode === "view";
  
  const initialType = id as "accounts_receivable" | "accounts_payable" | "transfer" || "accounts_receivable";
  
  useEffect(() => {
    setIsLoading(true);
    
    if (isCreating) {
      setIsLoading(false);
      return;
    }
    
    const fetchTransaction = async () => {
      try {
        if (id && id !== "new") {
          const transactionData = await transactionService.getTransactionById(id);
          setTransaction(transactionData);
        }
      } catch (error) {
        console.error("Erro ao buscar detalhes da transação:", error);
        useToast({
          title: "Transação não encontrada",
          description: "A transação solicitada não existe",
          variant: "destructive",
        });
        toast.error("Não foi possível encontrar a transação solicitada");
        navigate("/transactions");
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchTransaction();
  }, [id, navigate, isCreating]);
  
  const handleSubmit = async (data: TransactionFormValues) => {
    try {
      setIsLoading(true);
      
      // 🐛 DEBUG: Logs para validar as hipóteses
      console.log('🐛 DEBUG - useTransactionDetail: Dados recebidos do formulário:', data);
      console.log('🐛 DEBUG - useTransactionDetail: activeCompanyId do hook:', activeCompanyId);
      console.log('🐛 DEBUG - useTransactionDetail: localStorage activeCompanyId:', localStorage.getItem('activeCompanyId'));
      console.log('🐛 DEBUG - useTransactionDetail: Tipo do amount:', typeof data.amount, 'Valor:', data.amount);
      
      // Validar se temos um companyId válido
      if (!activeCompanyId) {
        console.error('🐛 ERROR - useTransactionDetail: activeCompanyId não está disponível!');
        throw new Error('ID da empresa não está disponível. Tente recarregar a página.');
      }
      
      // Converter os dados do formulário para o formato esperado pela API
      const transactionType = data.type === 'accounts_receivable' ? 'income' as const :
                             data.type === 'accounts_payable' ? 'expense' as const : 'transfer' as const;
      
      console.log('🐛 DEBUG - useTransactionDetail: Tipo convertido:', transactionType);
      
      // 🔧 CORREÇÃO: Criar payload tipado conforme CreateTransactionRequest
      const apiData: CreateTransactionRequest = {
        companyId: activeCompanyId, // ← CORREÇÃO PRINCIPAL: Incluir o companyId
        type: transactionType,
        description: data.description,
        amount: data.amount,
        transactionDate: data.transactionDate.toISOString(),
        bankAccountId: data.bankAccountId,
        notes: data.notes
      };

      // Adicionar campos específicos baseados no tipo de transação
      if (transactionType === 'transfer' && data.destinationAccountId) {
        apiData.destinationAccountId = data.destinationAccountId;
      }
      
      // 🔧 CORREÇÃO: Remover IDs hardcoded - eles devem vir do formulário ou ser opcionais
      // O backend pode não exigir esses campos para transações simples
      console.log('🐛 DEBUG - useTransactionDetail: Tipo de transação:', transactionType);
      console.log('🐛 DEBUG - useTransactionDetail: data.relatedId:', data.relatedId);
      
      // Adicionar campos opcionais se disponíveis
      if (data.relatedId) {
        apiData.categoryId = data.relatedId;
      }

      console.log('🐛 DEBUG - useTransactionDetail: Data original:', data.transactionDate);
      console.log('🐛 DEBUG - useTransactionDetail: Data formatada:', apiData.transactionDate);
      console.log('🐛 DEBUG - useTransactionDetail: Payload final que será enviado para a API:', JSON.stringify(apiData, null, 2));
      
      // 🐛 DEBUG: Validar se todos os campos obrigatórios estão presentes
      const requiredFields = ['companyId', 'type', 'description', 'amount', 'transactionDate', 'bankAccountId'];
      const missingFields = requiredFields.filter(field => !apiData[field]);
      if (missingFields.length > 0) {
        console.error('🐛 ERROR - useTransactionDetail: Campos obrigatórios ausentes:', missingFields);
      }
      
      if (isEditing && transaction) {
        console.log('🐛 DEBUG - useTransactionDetail: Atualizando transação existente:', transaction.id);
        await transactionService.updateTransaction(transaction.id, apiData);
        useToast({
          title: "Transação atualizada",
          description: "Transação atualizada com sucesso",
        });
        toast.success("Transação atualizada com sucesso");
      } else {
        console.log('🐛 DEBUG - useTransactionDetail: Criando nova transação...');
        const result = await transactionService.createTransaction(apiData);
        console.log('🐛 DEBUG - useTransactionDetail: Transação criada com sucesso:', result);
        useToast({
          title: "Transação criada",
          description: "Transação criada com sucesso",
        });
        toast.success("Transação criada com sucesso");
      }
      
      navigate("/transactions");
    } catch (error: any) {
      console.error("🐛 ERROR - useTransactionDetail: Erro ao salvar transação:", error);
      
      // 🐛 DEBUG: Logs detalhados do erro
      if (error.response) {
        console.error("🐛 ERROR - useTransactionDetail: Status da resposta:", error.response.status);
        console.error("🐛 ERROR - useTransactionDetail: Dados da resposta:", error.response.data);
        console.error("🐛 ERROR - useTransactionDetail: Headers da resposta:", error.response.headers);
      } else if (error.request) {
        console.error("🐛 ERROR - useTransactionDetail: Requisição feita mas sem resposta:", error.request);
      } else {
        console.error("🐛 ERROR - useTransactionDetail: Erro na configuração da requisição:", error.message);
      }
      
      // Mostrar erro mais específico se disponível
      const errorMessage = error.response?.data?.message || error.message || "Ocorreu um erro ao salvar a transação";
      
      useToast({
        title: "Erro ao salvar",
        description: errorMessage,
        variant: "destructive",
      });
      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };
  
  return {
    transaction,
    isLoading,
    isCreating,
    isEditing,
    isViewing,
    initialType,
    handleSubmit
  };
}
